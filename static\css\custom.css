/* Custom CSS for Heart Disease Prediction System */

/* Enhanced animations and transitions */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Loading animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced form styling */
.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Prediction result styling */
.prediction-card {
    border-left: 5px solid;
    margin-bottom: 1rem;
}

.prediction-card.positive {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.prediction-card.negative {
    border-left-color: #28a745;
    background-color: #d4edda;
}

/* Enhanced progress bars */
.confidence-progress {
    height: 25px;
    border-radius: 15px;
    overflow: hidden;
    background-color: #e9ecef;
}

.confidence-progress .progress-bar {
    border-radius: 15px;
    transition: width 0.6s ease;
}

/* Feature importance chart styling */
.feature-chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

/* Risk factor badges */
.risk-badge {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.risk-high {
    background-color: #dc3545;
    color: white;
}

.risk-medium {
    background-color: #ffc107;
    color: #212529;
}

.risk-low {
    background-color: #28a745;
    color: white;
}

/* Enhanced table styling */
.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.1);
}

/* Statistics cards */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    margin-bottom: 2rem;
}

.stat-card h3 {
    font-size: 3rem;
    font-weight: bold;
    margin: 1rem 0;
}

.stat-card i {
    font-size: 3rem;
    opacity: 0.8;
}

/* Enhanced buttons */
.btn-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .hero-section {
        padding: 2rem 0;
    }
    
    .hero-section h1 {
        font-size: 2rem;
    }
    
    .stat-card h3 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
}

/* Print styles */
@media print {
    .navbar, .btn, footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #dee2e6 !important;
        box-shadow: none !important;
    }
    
    .hero-section {
        background: white !important;
        color: black !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        border-color: #4a5568;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

/* Accessibility improvements */
.btn:focus, .form-control:focus, .form-select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .btn-primary {
        background-color: #000;
        border-color: #000;
    }
    
    .text-primary {
        color: #000 !important;
    }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
