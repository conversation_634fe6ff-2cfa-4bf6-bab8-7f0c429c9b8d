#!/usr/bin/env python3
"""
Demo setup script for Heart Disease Prediction System
Prepares the system for thesis defense demonstration
"""

import os
import sys
import django
import subprocess

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings')
django.setup()

from django.core.management import execute_from_command_line

def run_command(command, description):
    """Run a command and print status"""
    print(f"\n{'='*50}")
    print(f"🔄 {description}")
    print(f"{'='*50}")
    
    try:
        if isinstance(command, list):
            result = subprocess.run(command, check=True, capture_output=True, text=True)
            if result.stdout:
                print(result.stdout)
        else:
            execute_from_command_line(command.split())
        print(f"✅ {description} - COMPLETED")
        return True
    except Exception as e:
        print(f"❌ {description} - FAILED: {str(e)}")
        return False

def main():
    """Main demo setup function"""
    print("🎓 Heart Disease Prediction System - Demo Setup")
    print("📚 MSc Computer Science Thesis Project")
    print("🏫 University of Ghana - Gilles Ashley")
    print("\nPreparing system for thesis defense demonstration...\n")
    
    # List of setup commands
    setup_steps = [
        ("python manage.py makemigrations", "Creating database migrations"),
        ("python manage.py migrate", "Applying database migrations"),
        ("python manage.py test_models", "Testing ML models"),
        ("python manage.py create_sample_data --count 15", "Creating sample patient data"),
    ]
    
    success_count = 0
    total_steps = len(setup_steps)
    
    for command, description in setup_steps:
        if run_command(command, description):
            success_count += 1
    
    print(f"\n{'='*60}")
    print(f"📊 SETUP SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Completed: {success_count}/{total_steps} steps")
    
    if success_count == total_steps:
        print("🎉 Demo setup completed successfully!")
        print("\n📋 NEXT STEPS FOR THESIS DEFENSE:")
        print("1. Run: python manage.py runserver")
        print("2. Open browser to: http://127.0.0.1:8000")
        print("3. Navigate through the application:")
        print("   - Home page: Overview and statistics")
        print("   - New Prediction: Input patient data")
        print("   - Results: View predictions with confidence")
        print("   - History: Review past predictions")
        print("   - About: Project information")
        print("\n🎯 DEMO SCENARIOS:")
        print("• High-risk patient: Age 65, Male, High BP, High Cholesterol")
        print("• Low-risk patient: Age 35, Female, Normal vitals")
        print("• Compare different ML models")
        print("• Show ensemble prediction")
        print("• Demonstrate confidence scoring")
        print("• Display feature importance charts")
        
    else:
        print("⚠️  Some setup steps failed. Please check the errors above.")
        print("You may need to:")
        print("- Ensure all dependencies are installed")
        print("- Check that ML model files exist in models/ directory")
        print("- Verify database permissions")
    
    print(f"\n{'='*60}")
    print("🚀 Ready for thesis defense demonstration!")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
