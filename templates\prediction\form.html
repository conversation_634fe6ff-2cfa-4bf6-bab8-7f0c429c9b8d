{% extends 'base.html' %} {% block title %}New Prediction - Heart Disease
Prediction System{% endblock %} {% block content %}
<div class="container py-5">
  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h3 class="mb-0">
            <i class="bi bi-clipboard-data"></i> Heart Disease Prediction Form
          </h3>
          <p class="mb-0">
            Enter patient data to get a heart disease risk assessment
          </p>
        </div>
        <div class="card-body">
          <form method="post" id="predictionForm">
            {% csrf_token %}

            <!-- Model Selection -->
            <div class="row mb-4">
              <div class="col-md-12">
                <div class="card bg-light">
                  <div class="card-body">
                    <h5 class="card-title">
                      <i class="bi bi-cpu"></i> Model Selection
                    </h5>
                    <div class="row">
                      <div class="col-md-8">
                        <label
                          for="{{ model_form.selected_model.id_for_label }}"
                          class="form-label"
                        >
                          {{ model_form.selected_model.label }}
                        </label>
                        {{ model_form.selected_model }}
                      </div>
                      <div class="col-md-4">
                        <div class="form-check mt-4">
                          {{ model_form.use_ensemble }}
                          <label
                            class="form-check-label"
                            for="{{ model_form.use_ensemble.id_for_label }}"
                          >
                            {{ model_form.use_ensemble.label }}
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Patient Demographics -->
            <div class="row mb-4">
              <div class="col-md-12">
                <h5 class="text-primary">
                  <i class="bi bi-person"></i> Patient Demographics
                </h5>
                <hr />
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.age.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.age.label }}
                </label>
                {{ patient_form.age }} {% if patient_form.age.help_text %}
                <div class="form-text">{{ patient_form.age.help_text }}</div>
                {% endif %}
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.sex.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.sex.label }}
                </label>
                {{ patient_form.sex }}
              </div>
            </div>

            <!-- Symptoms and Clinical Signs -->
            <div class="row mb-4">
              <div class="col-md-12">
                <h5 class="text-primary">
                  <i class="bi bi-heart"></i> Symptoms & Clinical Signs
                </h5>
                <hr />
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.cp.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.cp.label }}
                </label>
                {{ patient_form.cp }}
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.exang.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.exang.label }}
                </label>
                {{ patient_form.exang }}
              </div>
            </div>

            <!-- Vital Signs -->
            <div class="row mb-4">
              <div class="col-md-12">
                <h5 class="text-primary">
                  <i class="bi bi-activity"></i> Vital Signs
                </h5>
                <hr />
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.trestbps.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.trestbps.label }}
                </label>
                {{ patient_form.trestbps }} {% if
                patient_form.trestbps.help_text %}
                <div class="form-text">
                  {{ patient_form.trestbps.help_text }}
                </div>
                {% endif %}
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.thalach.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.thalach.label }}
                </label>
                {{ patient_form.thalach }} {% if patient_form.thalach.help_text
                %}
                <div class="form-text">
                  {{ patient_form.thalach.help_text }}
                </div>
                {% endif %}
              </div>
            </div>

            <!-- Laboratory Results -->
            <div class="row mb-4">
              <div class="col-md-12">
                <h5 class="text-primary">
                  <i class="bi bi-clipboard-pulse"></i> Laboratory Results
                </h5>
                <hr />
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.chol.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.chol.label }}
                </label>
                {{ patient_form.chol }} {% if patient_form.chol.help_text %}
                <div class="form-text">{{ patient_form.chol.help_text }}</div>
                {% endif %}
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.fbs.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.fbs.label }}
                </label>
                {{ patient_form.fbs }}
              </div>
            </div>

            <!-- ECG and Exercise Test Results -->
            <div class="row mb-4">
              <div class="col-md-12">
                <h5 class="text-primary">
                  <i class="bi bi-graph-up"></i> ECG & Exercise Test Results
                </h5>
                <hr />
              </div>
              <div class="col-md-4 mb-3">
                <label
                  for="{{ patient_form.restecg.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.restecg.label }}
                </label>
                {{ patient_form.restecg }}
              </div>
              <div class="col-md-4 mb-3">
                <label
                  for="{{ patient_form.oldpeak.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.oldpeak.label }}
                </label>
                {{ patient_form.oldpeak }} {% if patient_form.oldpeak.help_text
                %}
                <div class="form-text">
                  {{ patient_form.oldpeak.help_text }}
                </div>
                {% endif %}
              </div>
              <div class="col-md-4 mb-3">
                <label
                  for="{{ patient_form.slope.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.slope.label }}
                </label>
                {{ patient_form.slope }}
              </div>
            </div>

            <!-- Advanced Tests -->
            <div class="row mb-4">
              <div class="col-md-12">
                <h5 class="text-primary">
                  <i class="bi bi-search"></i> Advanced Tests
                </h5>
                <hr />
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.ca.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.ca.label }}
                </label>
                {{ patient_form.ca }} {% if patient_form.ca.help_text %}
                <div class="form-text">{{ patient_form.ca.help_text }}</div>
                {% endif %}
              </div>
              <div class="col-md-6 mb-3">
                <label
                  for="{{ patient_form.thal.id_for_label }}"
                  class="form-label"
                >
                  {{ patient_form.thal.label }}
                </label>
                {{ patient_form.thal }} {% if patient_form.thal.help_text %}
                <div class="form-text">{{ patient_form.thal.help_text }}</div>
                {% endif %}
              </div>
            </div>

            <!-- Submit Button -->
            <div class="row">
              <div class="col-md-12 text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                  <i class="bi bi-cpu"></i> Generate Prediction
                </button>
                <a
                  href="{% url 'prediction:home' %}"
                  class="btn btn-outline-secondary btn-lg ms-3"
                >
                  <i class="bi bi-arrow-left"></i> Cancel
                </a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %} {% block extra_js %}
<script>
  document
    .getElementById("predictionForm")
    .addEventListener("submit", function (e) {
      // Show loading state
      const submitBtn = this.querySelector('button[type="submit"]');
      const originalText = submitBtn.innerHTML;
      submitBtn.innerHTML =
        '<i class="bi bi-hourglass-split"></i> Processing...';
      submitBtn.disabled = true;

      // Re-enable button after 10 seconds as fallback
      setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
      }, 10000);
    });
</script>
{% endblock %}
