#!/usr/bin/env python3
"""
Comprehensive Django fix and setup script
"""

import os
import sys
import subprocess
import django

def run_command(command, description):
    """Run a command and return success status"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, cwd=os.getcwd())
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} - FAILED")
            if result.stderr.strip():
                print(f"   Error: {result.stderr.strip()}")
            return False
    except Exception as e:
        print(f"❌ {description} - EXCEPTION: {e}")
        return False

def setup_django():
    """Set up Django environment"""
    print("🎓 Heart Disease Prediction System - Django Setup & Fix")
    print("=" * 60)
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings')
    
    # Step 1: Check Python and packages
    print("\n1. Checking Python environment...")
    success = True
    
    commands = [
        ("python --version", "Python version check"),
        ("pip list | findstr Django", "Django installation check"),
        ("pip list | findstr scikit-learn", "Scikit-learn installation check"),
        ("pip list | findstr joblib", "Joblib installation check"),
    ]
    
    for cmd, desc in commands:
        if not run_command(cmd, desc):
            success = False
    
    # Step 2: Django setup
    print("\n2. Setting up Django...")
    
    try:
        import django
        print(f"✅ Django imported successfully - Version: {django.get_version()}")
        django.setup()
        print("✅ Django setup completed")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        success = False
    
    # Step 3: Database setup
    print("\n3. Setting up database...")
    
    db_commands = [
        ("python manage.py makemigrations", "Creating migrations"),
        ("python manage.py migrate", "Applying migrations"),
    ]
    
    for cmd, desc in db_commands:
        if not run_command(cmd, desc):
            success = False
    
    # Step 4: Test Django components
    print("\n4. Testing Django components...")
    
    try:
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        from prediction.models import PatientData
        count = PatientData.objects.count()
        print(f"✅ PatientData model working - Records: {count}")
        
        from prediction.ml_utils import predictor
        models = predictor.get_available_models()
        print(f"✅ ML models loaded: {models}")
        
        from django.urls import reverse
        home_url = reverse('prediction:home')
        print(f"✅ URL routing working - Home URL: {home_url}")
        
    except Exception as e:
        print(f"❌ Django component test failed: {e}")
        success = False
    
    # Step 5: Create sample data
    print("\n5. Creating sample data...")
    if not run_command("python manage.py create_sample_data --count 5", "Creating sample patient data"):
        print("⚠️  Sample data creation failed, but this is not critical")
    
    # Step 6: Final status
    print("\n" + "=" * 60)
    if success:
        print("🎉 Django setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Run: python manage.py runserver")
        print("2. Open browser to: http://127.0.0.1:8000")
        print("3. Test the heart disease prediction system")
    else:
        print("⚠️  Some issues were found. Please review the errors above.")
    
    return success

if __name__ == "__main__":
    setup_django()
