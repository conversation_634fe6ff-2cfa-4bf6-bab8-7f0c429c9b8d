# Heart Disease Prediction System

A Django web application for heart disease prediction using machine learning models. This system was developed as part of an MSc Computer Science thesis project at the University of Ghana.

## Project Overview

**Title:** Heart Disease Prediction Using Hybrid Machine Learning Models: Early Detection Through Advanced Feature Selection and Ensemble Methods

**Author:** <PERSON> (Student ID: 22254414)  
**Supervisor:** Prof. <PERSON><PERSON>  
**Institution:** University of Ghana, Department of Computer Science

## Features

- **Multiple ML Models**: Random Forest, SVM, Logistic Regression, Gradient Boosting
- **Ensemble Predictions**: Combine multiple models for improved accuracy
- **Real-time Predictions**: Instant heart disease risk assessment
- **Confidence Scoring**: Get confidence levels for each prediction
- **Feature Importance**: Visualize which factors contribute most to predictions
- **Professional UI**: Bootstrap-based responsive design
- **Clinical Ready**: Designed for healthcare professionals
- **Prediction History**: Track and review past predictions

## Installation & Setup

### Prerequisites

- Python 3.8+
- Virtual environment (recommended)

### Installation Steps

1. **Clone or navigate to the project directory:**
   ```bash
   cd heart_disease
   ```

2. **Activate the virtual environment:**
   ```bash
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run database migrations:**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

5. **Test the ML models:**
   ```bash
   python manage.py test_models
   ```

6. **Create sample data (optional):**
   ```bash
   python manage.py create_sample_data --count 20
   ```

7. **Run the development server:**
   ```bash
   python manage.py runserver
   ```

8. **Access the application:**
   Open your browser and go to `http://127.0.0.1:8000`

## Usage

### Making Predictions

1. **Navigate to "New Prediction"** from the main menu
2. **Select ML Model** or choose ensemble prediction
3. **Enter Patient Data:**
   - Demographics (age, sex)
   - Symptoms (chest pain type, exercise angina)
   - Vital signs (blood pressure, heart rate)
   - Lab results (cholesterol, blood sugar)
   - Test results (ECG, stress test, fluoroscopy)
4. **Submit** to get instant prediction results
5. **View Results** with confidence scores and risk analysis

### Features Explained

#### Input Features (13 total):
- **Age**: Patient age in years
- **Sex**: Male (1) or Female (0)
- **Chest Pain Type**: 4 categories from typical angina to asymptomatic
- **Resting Blood Pressure**: In mm Hg
- **Cholesterol**: Serum cholesterol in mg/dl
- **Fasting Blood Sugar**: >120 mg/dl (1=true, 0=false)
- **Resting ECG**: Normal, ST-T abnormality, or LV hypertrophy
- **Max Heart Rate**: Maximum heart rate achieved
- **Exercise Angina**: Exercise-induced angina (1=yes, 0=no)
- **ST Depression**: Exercise-induced ST depression
- **ST Slope**: Upsloping, flat, or downsloping
- **Major Vessels**: Number of vessels colored by fluoroscopy (0-3)
- **Thalassemia**: Normal, fixed defect, or reversible defect

#### Available Models:
- **Random Forest**: Ensemble of decision trees
- **SVM**: Support Vector Machine with optimal boundary finding
- **Logistic Regression**: Linear model with logistic function
- **Gradient Boosting**: Sequential ensemble method
- **Ensemble**: Combines all models using majority voting

## Project Structure

```
heart_disease/
├── src/                    # Django project settings
├── prediction/             # Main Django app
│   ├── models.py          # Patient data model
│   ├── forms.py           # Input forms
│   ├── views.py           # Application views
│   ├── ml_utils.py        # ML model integration
│   └── management/        # Custom management commands
├── templates/             # HTML templates
├── static/               # CSS, JS, images
├── models/               # Pre-trained ML models (.joblib)
├── requirements.txt      # Python dependencies
└── README.md            # This file
```

## API Usage

The system provides a REST API endpoint for programmatic access:

```bash
POST /api/predict/
Content-Type: application/json

{
    "age": 63,
    "sex": 1,
    "cp": 3,
    "trestbps": 145,
    "chol": 233,
    "fbs": 1,
    "restecg": 0,
    "thalach": 150,
    "exang": 0,
    "oldpeak": 2.3,
    "slope": 0,
    "ca": 0,
    "thal": 1,
    "model": "RandomForest",
    "use_ensemble": false
}
```

## Model Information

The ML models were trained on the Cleveland and Statlog heart disease datasets from the UCI Machine Learning Repository. The models use advanced preprocessing techniques including:

- Missing value imputation (MICE)
- Data balancing (SMOTE-ENN)
- Feature scaling and normalization
- Outlier detection and removal

## Performance

The models achieve high accuracy rates (>95%) on the test datasets, making them suitable for clinical decision support.

## Thesis Defense Demo

This application serves as a practical demonstration of the research findings for thesis defense. Key demonstration points:

1. **Real-time Predictions**: Show instant results with confidence scores
2. **Multiple Models**: Compare different ML algorithms
3. **Ensemble Methods**: Demonstrate improved accuracy through model combination
4. **Clinical Interface**: Professional UI suitable for healthcare settings
5. **Feature Analysis**: Visualize important factors in heart disease prediction

## Troubleshooting

### Common Issues:

1. **Models not loading**: Ensure .joblib files are in the `models/` directory
2. **Import errors**: Check that all dependencies are installed
3. **Database errors**: Run migrations with `python manage.py migrate`
4. **Static files not loading**: Run `python manage.py collectstatic` if needed

### Testing:

```bash
# Test ML models
python manage.py test_models

# Create sample data
python manage.py create_sample_data --count 10

# Run Django tests
python manage.py test
```

## License

This project is part of academic research at the University of Ghana. All rights reserved.

## Contact

**Gilles Ashley**  
MSc Computer Science Student  
University of Ghana  
Email: [Contact through university]

---

*Developed for MSc Computer Science Thesis Defense - September 2025*
