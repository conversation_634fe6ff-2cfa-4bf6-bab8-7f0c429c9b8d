#!/usr/bin/env python3
"""
Debug Django startup issues
"""

import os
import sys
import traceback

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings')

try:
    print("1. Importing Django...")
    import django
    print(f"   Django version: {django.get_version()}")
    
    print("2. Setting up Django...")
    django.setup()
    print("   Django setup complete")
    
    print("3. Testing database connection...")
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute("SELECT 1")
    result = cursor.fetchone()
    print(f"   Database test result: {result}")
    
    print("4. Testing Django apps...")
    from django.apps import apps
    installed_apps = [app.name for app in apps.get_app_configs()]
    print(f"   Installed apps: {installed_apps}")
    
    print("5. Testing prediction app...")
    from prediction.models import PatientData
    count = PatientData.objects.count()
    print(f"   Patient records: {count}")
    
    print("6. Testing ML models...")
    from prediction.ml_utils import predictor
    available_models = predictor.get_available_models()
    print(f"   Available ML models: {available_models}")
    
    print("7. Testing URL configuration...")
    from django.urls import reverse
    try:
        home_url = reverse('prediction:home')
        print(f"   Home URL: {home_url}")
    except Exception as e:
        print(f"   URL error: {e}")
    
    print("8. Testing views...")
    from prediction.views import HomeView
    print(f"   HomeView class: {HomeView}")
    
    print("\n✅ All Django components loaded successfully!")
    print("🚀 Django should be ready to run with: python manage.py runserver")
    
except Exception as e:
    print(f"\n❌ Error during Django setup: {e}")
    print("\nFull traceback:")
    traceback.print_exc()
    
    print("\n🔍 Debugging information:")
    print(f"Python path: {sys.path}")
    print(f"Current directory: {os.getcwd()}")
    print(f"DJANGO_SETTINGS_MODULE: {os.environ.get('DJANGO_SETTINGS_MODULE')}")
    
    # Check if files exist
    files_to_check = [
        'manage.py',
        'src/settings.py',
        'src/urls.py',
        'prediction/models.py',
        'prediction/views.py',
        'prediction/urls.py',
        'models/RandomForest_model.joblib'
    ]
    
    print("\n📁 File existence check:")
    for file_path in files_to_check:
        exists = os.path.exists(file_path)
        print(f"   {file_path}: {'✅' if exists else '❌'}")
