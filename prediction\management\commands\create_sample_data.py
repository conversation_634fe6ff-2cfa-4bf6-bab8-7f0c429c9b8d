from django.core.management.base import BaseCommand
from prediction.models import PatientData
from prediction.ml_utils import make_prediction
import random

class Command(BaseCommand):
    help = 'Create sample patient data for demonstration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of sample patients to create'
        )

    def handle(self, *args, **options):
        count = options['count']
        self.stdout.write(f'Creating {count} sample patient records...')
        
        # Sample data templates
        sample_patients = [
            # High risk patients
            {
                'age': random.randint(55, 75),
                'sex': 1,  # Male
                'cp': random.choice([3, 4]),  # Non-anginal or asymptomatic
                'trestbps': random.randint(140, 180),
                'chol': random.randint(240, 350),
                'fbs': 1,  # High blood sugar
                'restecg': random.choice([1, 2]),
                'thalach': random.randint(100, 150),
                'exang': 1,  # Exercise angina
                'oldpeak': random.uniform(1.0, 4.0),
                'slope': random.choice([2, 3]),
                'ca': random.choice([1, 2, 3]),
                'thal': random.choice([6, 7])
            },
            # Low risk patients
            {
                'age': random.randint(25, 45),
                'sex': random.choice([0, 1]),
                'cp': random.choice([1, 2]),  # Typical or atypical angina
                'trestbps': random.randint(110, 130),
                'chol': random.randint(150, 220),
                'fbs': 0,  # Normal blood sugar
                'restecg': 0,  # Normal ECG
                'thalach': random.randint(160, 200),
                'exang': 0,  # No exercise angina
                'oldpeak': random.uniform(0.0, 1.0),
                'slope': 1,  # Upsloping
                'ca': 0,
                'thal': 3  # Normal
            },
            # Medium risk patients
            {
                'age': random.randint(45, 60),
                'sex': random.choice([0, 1]),
                'cp': random.choice([2, 3]),
                'trestbps': random.randint(130, 150),
                'chol': random.randint(200, 260),
                'fbs': random.choice([0, 1]),
                'restecg': random.choice([0, 1]),
                'thalach': random.randint(140, 180),
                'exang': random.choice([0, 1]),
                'oldpeak': random.uniform(0.5, 2.5),
                'slope': random.choice([1, 2]),
                'ca': random.choice([0, 1]),
                'thal': random.choice([3, 6])
            }
        ]
        
        created_count = 0
        models = ['RandomForest', 'SVM', 'LogisticRegression', 'GradientBoosting']
        
        for i in range(count):
            # Choose a random template
            template = random.choice(sample_patients)
            
            # Add some variation
            patient_data = {}
            for key, value in template.items():
                if isinstance(value, int):
                    # Add ±10% variation for integers
                    variation = int(value * 0.1)
                    patient_data[key] = max(1, value + random.randint(-variation, variation))
                elif isinstance(value, float):
                    # Add ±20% variation for floats
                    variation = value * 0.2
                    patient_data[key] = max(0.0, value + random.uniform(-variation, variation))
                else:
                    patient_data[key] = value
            
            try:
                # Create patient record
                patient = PatientData.objects.create(**patient_data)
                
                # Make prediction
                selected_model = random.choice(models)
                use_ensemble = random.choice([True, False])
                
                result = make_prediction(
                    patient, 
                    model_name=selected_model, 
                    use_ensemble=use_ensemble
                )
                
                if result['success']:
                    patient.prediction_result = result['prediction']
                    patient.prediction_confidence = result['confidence']
                    patient.model_used = result['model_used']
                    patient.save()
                    
                    created_count += 1
                    prediction_text = 'Heart Disease' if result['prediction'] == 1 else 'No Heart Disease'
                    self.stdout.write(
                        f"Created patient {patient.id}: Age {patient.age}, "
                        f"{'Male' if patient.sex == 1 else 'Female'} -> {prediction_text}"
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f"Created patient {patient.id} but prediction failed: {result['error']}")
                    )
                    created_count += 1
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error creating patient {i+1}: {str(e)}")
                )
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} sample patient records!')
        )
