{% extends 'base.html' %}

{% block title %}Prediction Result - Heart Disease Prediction System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Prediction Result Card -->
            <div class="card mb-4">
                <div class="card-header {% if patient.prediction_result == 1 %}bg-danger{% else %}bg-success{% endif %} text-white">
                    <h3 class="mb-0">
                        <i class="bi bi-{% if patient.prediction_result == 1 %}exclamation-triangle{% else %}check-circle{% endif %}"></i>
                        Prediction Result
                    </h3>
                </div>
                <div class="card-body text-center">
                    <div class="row">
                        <div class="col-md-6">
                            <h2 class="{% if patient.prediction_result == 1 %}prediction-positive{% else %}prediction-negative{% endif %}">
                                {{ prediction_text }}
                            </h2>
                            <p class="lead">Model Used: <strong>{{ patient.model_used }}</strong></p>
                        </div>
                        <div class="col-md-6">
                            <h4>Confidence Score</h4>
                            <div class="progress mb-2" style="height: 30px;">
                                <div class="progress-bar {% if patient.prediction_result == 1 %}bg-danger{% else %}bg-success{% endif %}" 
                                     role="progressbar" 
                                     style="width: {{ confidence_percentage }}%">
                                    {{ confidence_percentage }}%
                                </div>
                            </div>
                            <small class="text-muted">Prediction confidence level</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Patient Information -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-person-badge"></i> Patient Information
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Age:</strong></td>
                                    <td>{{ feature_data.Age }} years</td>
                                </tr>
                                <tr>
                                    <td><strong>Sex:</strong></td>
                                    <td>{{ feature_data.Sex }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Chest Pain Type:</strong></td>
                                    <td>{{ feature_data.Chest Pain Type }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Resting Blood Pressure:</strong></td>
                                    <td>{{ feature_data.Resting BP }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Cholesterol:</strong></td>
                                    <td>{{ feature_data.Cholesterol }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Fasting Blood Sugar > 120:</strong></td>
                                    <td>{{ feature_data.Fasting Blood Sugar > 120 }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Resting ECG:</strong></td>
                                    <td>{{ feature_data.Resting ECG }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Max Heart Rate:</strong></td>
                                    <td>{{ feature_data.Max Heart Rate }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Exercise Angina:</strong></td>
                                    <td>{{ feature_data.Exercise Angina }}</td>
                                </tr>
                                <tr>
                                    <td><strong>ST Depression:</strong></td>
                                    <td>{{ feature_data.ST Depression }}</td>
                                </tr>
                                <tr>
                                    <td><strong>ST Slope:</strong></td>
                                    <td>{{ feature_data.ST Slope }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Major Vessels:</strong></td>
                                    <td>{{ feature_data.Major Vessels }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Thalassemia:</strong></td>
                                    <td>{{ feature_data.Thalassemia }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Prediction Date:</strong></td>
                                    <td>{{ patient.created_at|date:"M d, Y H:i" }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Feature Importance Chart (if available) -->
            {% if feature_importance %}
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-bar-chart"></i> Feature Importance
                    </h4>
                </div>
                <div class="card-body">
                    <canvas id="featureImportanceChart" width="400" height="200"></canvas>
                </div>
            </div>
            {% endif %}

            <!-- Risk Factors Analysis -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="bi bi-shield-exclamation"></i> Risk Factors Analysis
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <h5>Key Risk Indicators:</h5>
                            <ul class="list-group list-group-flush">
                                {% if patient.age > 55 %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Age Factor ({{ patient.age }} years)
                                    <span class="badge bg-warning rounded-pill">High Risk</span>
                                </li>
                                {% endif %}
                                {% if patient.chol > 240 %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    High Cholesterol ({{ patient.chol }} mg/dl)
                                    <span class="badge bg-danger rounded-pill">Risk Factor</span>
                                </li>
                                {% endif %}
                                {% if patient.trestbps > 140 %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    High Blood Pressure ({{ patient.trestbps }} mm Hg)
                                    <span class="badge bg-danger rounded-pill">Risk Factor</span>
                                </li>
                                {% endif %}
                                {% if patient.fbs == 1 %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Elevated Fasting Blood Sugar
                                    <span class="badge bg-warning rounded-pill">Risk Factor</span>
                                </li>
                                {% endif %}
                                {% if patient.exang == 1 %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Exercise-Induced Angina
                                    <span class="badge bg-danger rounded-pill">Risk Factor</span>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body text-center">
                    <a href="{% url 'prediction:form' %}" class="btn btn-primary btn-lg me-3">
                        <i class="bi bi-plus-circle"></i> New Prediction
                    </a>
                    <a href="{% url 'prediction:history' %}" class="btn btn-outline-secondary btn-lg me-3">
                        <i class="bi bi-clock-history"></i> View History
                    </a>
                    <button onclick="window.print()" class="btn btn-outline-info btn-lg">
                        <i class="bi bi-printer"></i> Print Report
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% if feature_importance %}
<script>
// Feature Importance Chart
const ctx = document.getElementById('featureImportanceChart').getContext('2d');
const featureImportanceChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: [
            {% for feature, importance in feature_importance.items %}
            '{{ feature }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Feature Importance',
            data: [
                {% for feature, importance in feature_importance.items %}
                {{ importance }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.6)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Importance Score'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Features'
                }
            }
        },
        plugins: {
            title: {
                display: true,
                text: 'Feature Importance for {{ patient.model_used }} Model'
            }
        }
    }
});
</script>
{% endif %}
{% endblock %}
