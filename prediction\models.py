from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator

class PatientData(models.Model):
    """Model to store patient data for heart disease prediction"""

    # Patient demographics
    age = models.IntegerField(
        validators=[MinValueValida<PERSON>(1), MaxV<PERSON>ueValidator(120)],
        help_text="Age of the patient in years"
    )

    sex = models.IntegerField(
        choices=[(0, 'Female'), (1, 'Male')],
        help_text="Sex of the patient"
    )

    # Chest pain type
    cp = models.IntegerField(
        choices=[
            (1, 'Typical Angina'),
            (2, 'Atypical Angina'),
            (3, 'Non-Anginal Pain'),
            (4, 'Asymptomatic')
        ],
        help_text="Chest pain type"
    )

    # Vital signs and lab values
    trestbps = models.IntegerField(
        validators=[MinValueValidator(80), MaxValueValidator(250)],
        help_text="Resting blood pressure in mm Hg"
    )

    chol = models.IntegerField(
        validators=[MinValueValidator(100), MaxValueValidator(600)],
        help_text="Serum cholesterol level in mg/dl"
    )

    fbs = models.IntegerField(
        choices=[(0, 'False'), (1, 'True')],
        help_text="Fasting blood sugar > 120 mg/dl"
    )

    # ECG results
    restecg = models.IntegerField(
        choices=[
            (0, 'Normal'),
            (1, 'ST-T Wave Abnormality'),
            (2, 'Left Ventricular Hypertrophy')
        ],
        help_text="Resting electrocardiographic results"
    )

    thalach = models.IntegerField(
        validators=[MinValueValidator(60), MaxValueValidator(220)],
        help_text="Maximum heart rate achieved"
    )

    exang = models.IntegerField(
        choices=[(0, 'No'), (1, 'Yes')],
        help_text="Exercise induced angina"
    )

    oldpeak = models.FloatField(
        validators=[MinValueValidator(0.0), MaxValueValidator(10.0)],
        help_text="ST depression induced by exercise relative to rest"
    )

    slope = models.IntegerField(
        choices=[
            (1, 'Upsloping'),
            (2, 'Flat'),
            (3, 'Downsloping')
        ],
        help_text="Slope of the peak exercise ST segment"
    )

    ca = models.IntegerField(
        choices=[(0, '0'), (1, '1'), (2, '2'), (3, '3')],
        help_text="Number of major vessels colored by fluoroscopy"
    )

    thal = models.IntegerField(
        choices=[
            (3, 'Normal'),
            (6, 'Fixed Defect'),
            (7, 'Reversible Defect')
        ],
        help_text="Thalassemia test result"
    )

    # Prediction results
    prediction_result = models.IntegerField(
        choices=[(0, 'No Heart Disease'), (1, 'Heart Disease Present')],
        null=True, blank=True,
        help_text="Prediction result from ML model"
    )

    prediction_confidence = models.FloatField(
        null=True, blank=True,
        help_text="Confidence score of the prediction"
    )

    model_used = models.CharField(
        max_length=50, null=True, blank=True,
        help_text="ML model used for prediction"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Patient Data"
        verbose_name_plural = "Patient Data Records"

    def __str__(self):
        return f"Patient {self.id} - Age {self.age}, {'Male' if self.sex == 1 else 'Female'}"

    def get_feature_array(self):
        """Convert patient data to numpy array for ML model prediction"""
        return [
            self.age, self.sex, self.cp, self.trestbps, self.chol,
            self.fbs, self.restecg, self.thalach, self.exang,
            self.oldpeak, self.slope, self.ca, self.thal
        ]
