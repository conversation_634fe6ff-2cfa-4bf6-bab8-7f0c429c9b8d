#!/usr/bin/env python3
"""
Start Django server with proper error handling
"""

import os
import sys
import subprocess
import time
import threading
import socket

def check_port(host, port):
    """Check if a port is available"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def start_django_server():
    """Start Django development server"""
    print("🎓 Starting Heart Disease Prediction System...")
    print("=" * 50)
    
    # Set Django settings
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings')
    
    # Check if port is available
    port = 8000
    if check_port('127.0.0.1', port):
        print(f"⚠️  Port {port} is already in use. Trying port {port + 1}...")
        port += 1
    
    # Start server
    cmd = [sys.executable, 'manage.py', 'runserver', f'127.0.0.1:{port}']
    
    print(f"🚀 Starting Django server on http://127.0.0.1:{port}")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        # Start the server process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Print server output
        for line in iter(process.stdout.readline, ''):
            print(line.strip())
            
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        if process:
            process.terminate()
    except Exception as e:
        print(f"❌ Error starting server: {e}")

if __name__ == "__main__":
    start_django_server()
