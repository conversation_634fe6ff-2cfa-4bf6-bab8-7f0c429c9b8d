from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
import json

from .models import PatientData
from .forms import PatientDataForm, ModelSelectionForm
from .ml_utils import make_prediction, predictor

class HomeView(TemplateView):
    """Home page view"""
    template_name = 'prediction/home.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['available_models'] = predictor.get_available_models()
        context['total_predictions'] = PatientData.objects.count()
        return context

def prediction_form_view(request):
    """View for the prediction form"""
    if request.method == 'POST':
        patient_form = PatientDataForm(request.POST)
        model_form = ModelSelectionForm(request.POST)

        if patient_form.is_valid() and model_form.is_valid():
            # Save patient data
            patient = patient_form.save(commit=False)

            # Get model selection
            selected_model = model_form.cleaned_data['selected_model']
            use_ensemble = model_form.cleaned_data['use_ensemble']

            # Make prediction
            prediction_result = make_prediction(
                patient,
                model_name=selected_model,
                use_ensemble=use_ensemble
            )

            if prediction_result['success']:
                # Update patient record with prediction
                patient.prediction_result = prediction_result['prediction']
                patient.prediction_confidence = prediction_result['confidence']
                patient.model_used = prediction_result['model_used']
                patient.save()

                messages.success(request, 'Prediction completed successfully!')
                return redirect('prediction:result', patient_id=patient.id)
            else:
                messages.error(request, f'Prediction failed: {prediction_result["error"]}')
        else:
            messages.error(request, 'Please correct the errors in the form.')
    else:
        patient_form = PatientDataForm()
        model_form = ModelSelectionForm()

    context = {
        'patient_form': patient_form,
        'model_form': model_form,
        'available_models': predictor.get_available_models()
    }
    return render(request, 'prediction/form.html', context)

def prediction_result_view(request, patient_id):
    """View for displaying prediction results"""
    patient = get_object_or_404(PatientData, id=patient_id)

    # Get feature importance if available
    feature_importance = None
    if patient.model_used and patient.model_used != 'Ensemble':
        feature_importance = predictor.get_feature_importance(patient.model_used)

    # Prepare feature data for visualization
    feature_data = {
        'Age': patient.age,
        'Sex': 'Male' if patient.sex == 1 else 'Female',
        'Chest Pain Type': patient.get_cp_display(),
        'Resting BP': f"{patient.trestbps} mm Hg",
        'Cholesterol': f"{patient.chol} mg/dl",
        'Fasting Blood Sugar > 120': 'Yes' if patient.fbs == 1 else 'No',
        'Resting ECG': patient.get_restecg_display(),
        'Max Heart Rate': patient.thalach,
        'Exercise Angina': 'Yes' if patient.exang == 1 else 'No',
        'ST Depression': patient.oldpeak,
        'ST Slope': patient.get_slope_display(),
        'Major Vessels': patient.ca,
        'Thalassemia': patient.get_thal_display(),
    }

    context = {
        'patient': patient,
        'feature_data': feature_data,
        'feature_importance': feature_importance,
        'prediction_text': 'Heart Disease Present' if patient.prediction_result == 1 else 'No Heart Disease',
        'confidence_percentage': round(patient.prediction_confidence * 100, 1) if patient.prediction_confidence else 0
    }
    return render(request, 'prediction/result.html', context)

def history_view(request):
    """View for displaying prediction history"""
    patients = PatientData.objects.all()[:50]  # Show last 50 predictions

    context = {
        'patients': patients,
        'total_predictions': PatientData.objects.count(),
        'positive_predictions': PatientData.objects.filter(prediction_result=1).count(),
        'negative_predictions': PatientData.objects.filter(prediction_result=0).count(),
    }
    return render(request, 'prediction/history.html', context)

@csrf_exempt
def api_predict(request):
    """API endpoint for making predictions"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Only POST method allowed'}, status=405)

    try:
        data = json.loads(request.body)

        # Extract features from JSON data
        required_fields = [
            'age', 'sex', 'cp', 'trestbps', 'chol', 'fbs',
            'restecg', 'thalach', 'exang', 'oldpeak', 'slope', 'ca', 'thal'
        ]

        features = []
        for field in required_fields:
            if field not in data:
                return JsonResponse({'error': f'Missing field: {field}'}, status=400)
            features.append(data[field])

        # Get model selection
        model_name = data.get('model', 'RandomForest')
        use_ensemble = data.get('use_ensemble', False)

        # Make prediction
        result = make_prediction(features, model_name, use_ensemble)

        if result['success']:
            return JsonResponse({
                'prediction': result['prediction'],
                'confidence': result['confidence'],
                'model_used': result['model_used'],
                'prediction_text': 'Heart Disease Present' if result['prediction'] == 1 else 'No Heart Disease'
            })
        else:
            return JsonResponse({'error': result['error']}, status=500)

    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON data'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

def about_view(request):
    """About page with information about the models and methodology"""
    context = {
        'available_models': predictor.get_available_models(),
        'model_descriptions': {
            'RandomForest': 'An ensemble method that combines multiple decision trees to make predictions.',
            'SVM': 'Support Vector Machine that finds the optimal boundary between classes.',
            'LogisticRegression': 'A linear model that uses logistic function for binary classification.',
            'GradientBoosting': 'An ensemble method that builds models sequentially to correct errors.'
        }
    }
    return render(request, 'prediction/about.html', context)
