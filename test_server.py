#!/usr/bin/env python3
"""
Simple HTTP server test
"""

import http.server
import socketserver
import threading
import time

class <PERSON>Handler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Server</title>
        </head>
        <body>
            <h1>Test Server is Working!</h1>
            <p>This confirms that Python HTTP server can run on this system.</p>
            <p>Time: {}</p>
        </body>
        </html>
        """.format(time.strftime('%Y-%m-%d %H:%M:%S'))
        
        self.wfile.write(html.encode())

def start_test_server():
    PORT = 8001
    with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
        print(f"Test server running at http://127.0.0.1:{PORT}")
        httpd.serve_forever()

if __name__ == "__main__":
    start_test_server()
