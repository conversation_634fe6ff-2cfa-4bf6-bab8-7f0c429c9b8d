import os
import sys

# Add the project directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings')

try:
    import django
    print(f"Django version: {django.get_version()}")
    
    django.setup()
    print("Django setup successful")
    
    # Test database
    from django.db import connection
    cursor = connection.cursor()
    cursor.execute("SELECT 1")
    print("Database connection successful")
    
    # Test models
    from prediction.models import PatientData
    print(f"PatientData model loaded, count: {PatientData.objects.count()}")
    
    # Test ML utils
    from prediction.ml_utils import predictor
    models = predictor.get_available_models()
    print(f"Available ML models: {models}")
    
    print("All tests passed!")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
