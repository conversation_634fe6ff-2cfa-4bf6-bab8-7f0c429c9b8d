"""
Machine Learning utilities for heart disease prediction
"""

import numpy as np
import os
from django.conf import settings
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)

class HeartDiseasePredictor:
    """Class to handle ML model loading and predictions"""
    
    def __init__(self):
        self.models = {}
        self.models_dir = os.path.join(settings.BASE_DIR, 'models')
        self.model_files = {
            'RandomForest': 'RandomForest_model.joblib',
            'SVM': 'SVM_model.joblib',
            'LogisticRegression': 'LogisticRegression_model.joblib',
            'GradientBoosting': 'GradientBoosting_model.joblib'
        }
        self.load_models()
    
    def load_models(self):
        """Load all available ML models"""
        try:
            # Import joblib here so that importing this module doesn't fail
            # when joblib/scikit-learn aren't installed in the environment.
            try:
                import joblib
            except Exception:
                logger.warning("joblib not available — skipping model loading")
                return
            if not os.path.exists(self.models_dir):
                logger.warning(f"Models directory not found: {self.models_dir}")
                return

            for model_name, filename in self.model_files.items():
                model_path = os.path.join(self.models_dir, filename)
                try:
                    if os.path.exists(model_path):
                        self.models[model_name] = joblib.load(model_path)
                        logger.info(f"Successfully loaded {model_name} model")
                    else:
                        logger.warning(f"Model file not found: {model_path}")
                except Exception as e:
                    logger.error(f"Error loading {model_name} model: {e}")
        except Exception as e:
            logger.error(f"Error in load_models: {e}")
    
    def get_available_models(self) -> List[str]:
        """Get list of successfully loaded models"""
        return list(self.models.keys())
    
    def predict_single_model(self, features: List[float], model_name: str) -> Tuple[int, float]:
        """
        Make prediction using a single model
        
        Args:
            features: List of 13 feature values
            model_name: Name of the model to use
            
        Returns:
            Tuple of (prediction, confidence)
        """
        if model_name not in self.models:
            raise ValueError(f"Model {model_name} not available")
        
        model = self.models[model_name]
        features_array = np.array([features])
        
        # Get prediction
        prediction = model.predict(features_array)[0]
        
        # Get confidence (probability) if available
        confidence = 0.5  # Default confidence
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(features_array)[0]
            confidence = max(probabilities)  # Confidence is the max probability
        elif hasattr(model, 'decision_function'):
            # For SVM, use decision function
            decision_score = model.decision_function(features_array)[0]
            # Convert to probability-like score
            confidence = 1 / (1 + np.exp(-decision_score))
            if prediction == 0:
                confidence = 1 - confidence
        
        return int(prediction), float(confidence)
    
    def predict_ensemble(self, features: List[float]) -> Tuple[int, float, Dict[str, Tuple[int, float]]]:
        """
        Make ensemble prediction using all available models
        
        Args:
            features: List of 13 feature values
            
        Returns:
            Tuple of (ensemble_prediction, ensemble_confidence, individual_predictions)
        """
        if not self.models:
            raise ValueError("No models available for prediction")
        
        individual_predictions = {}
        predictions = []
        confidences = []
        
        # Get predictions from all models
        for model_name in self.models.keys():
            try:
                pred, conf = self.predict_single_model(features, model_name)
                individual_predictions[model_name] = (pred, conf)
                predictions.append(pred)
                confidences.append(conf)
            except Exception as e:
                logger.error(f"Error with {model_name} prediction: {e}")
        
        if not predictions:
            raise ValueError("No successful predictions from any model")
        
        # Ensemble prediction: majority vote
        ensemble_prediction = int(np.round(np.mean(predictions)))
        
        # Ensemble confidence: average confidence of models that agree with ensemble
        agreeing_confidences = [
            conf for pred, conf in zip(predictions, confidences) 
            if pred == ensemble_prediction
        ]
        ensemble_confidence = np.mean(agreeing_confidences) if agreeing_confidences else np.mean(confidences)
        
        return ensemble_prediction, float(ensemble_confidence), individual_predictions
    
    def get_feature_importance(self, model_name: str) -> Optional[Dict[str, float]]:
        """
        Get feature importance from a model if available
        
        Args:
            model_name: Name of the model
            
        Returns:
            Dictionary of feature names and their importance scores
        """
        if model_name not in self.models:
            return None
        
        model = self.models[model_name]
        
        # Feature names based on the dataset
        feature_names = [
            'age', 'sex', 'cp', 'trestbps', 'chol', 'fbs', 
            'restecg', 'thalach', 'exang', 'oldpeak', 
            'slope', 'ca', 'thal'
        ]
        
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
            return dict(zip(feature_names, importances))
        elif hasattr(model, 'coef_'):
            # For linear models, use absolute coefficients
            coefficients = np.abs(model.coef_[0])
            return dict(zip(feature_names, coefficients))
        
        return None

# Global predictor instance
predictor = HeartDiseasePredictor()

def make_prediction(patient_data, model_name: str = 'RandomForest', use_ensemble: bool = False):
    """
    Convenience function to make predictions
    
    Args:
        patient_data: PatientData model instance or list of features
        model_name: Name of the model to use
        use_ensemble: Whether to use ensemble prediction
        
    Returns:
        Dictionary with prediction results
    """
    # Extract features
    if hasattr(patient_data, 'get_feature_array'):
        features = patient_data.get_feature_array()
    else:
        features = patient_data
    
    try:
        if use_ensemble:
            prediction, confidence, individual_preds = predictor.predict_ensemble(features)
            return {
                'prediction': prediction,
                'confidence': confidence,
                'model_used': 'Ensemble',
                'individual_predictions': individual_preds,
                'success': True,
                'error': None
            }
        else:
            prediction, confidence = predictor.predict_single_model(features, model_name)
            return {
                'prediction': prediction,
                'confidence': confidence,
                'model_used': model_name,
                'individual_predictions': None,
                'success': True,
                'error': None
            }
    except Exception as e:
        logger.error(f"Prediction error: {e}")
        return {
            'prediction': None,
            'confidence': None,
            'model_used': None,
            'individual_predictions': None,
            'success': False,
            'error': str(e)
        }
