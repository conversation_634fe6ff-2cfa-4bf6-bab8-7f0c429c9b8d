from django.core.management.base import BaseCommand
from prediction.ml_utils import predictor, make_prediction
import numpy as np

class Command(BaseCommand):
    help = 'Test the ML models to ensure they work correctly'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing Heart Disease Prediction Models...'))
        
        # Test model loading
        available_models = predictor.get_available_models()
        self.stdout.write(f'Available models: {available_models}')
        
        if not available_models:
            self.stdout.write(self.style.ERROR('No models available! Check the models directory.'))
            return
        
        # Sample patient data for testing
        # Features: age, sex, cp, trestbps, chol, fbs, restecg, thalach, exang, oldpeak, slope, ca, thal
        test_cases = [
            {
                'name': 'High Risk Patient',
                'features': [63, 1, 3, 145, 233, 1, 0, 150, 0, 2.3, 0, 0, 1],
                'expected': 'High risk for heart disease'
            },
            {
                'name': 'Low Risk Patient', 
                'features': [37, 1, 2, 130, 250, 0, 1, 187, 0, 3.5, 0, 0, 2],
                'expected': 'Lower risk profile'
            },
            {
                'name': 'Female Patient',
                'features': [41, 0, 1, 130, 204, 0, 0, 172, 0, 1.4, 2, 0, 2],
                'expected': 'Moderate risk'
            }
        ]
        
        # Test each model
        for model_name in available_models:
            self.stdout.write(f'\n--- Testing {model_name} ---')
            
            for test_case in test_cases:
                try:
                    result = make_prediction(
                        test_case['features'], 
                        model_name=model_name, 
                        use_ensemble=False
                    )
                    
                    if result['success']:
                        prediction_text = 'Heart Disease' if result['prediction'] == 1 else 'No Heart Disease'
                        confidence = result['confidence'] * 100
                        
                        self.stdout.write(
                            f"  {test_case['name']}: {prediction_text} "
                            f"(Confidence: {confidence:.1f}%)"
                        )
                    else:
                        self.stdout.write(
                            self.style.ERROR(f"  {test_case['name']}: Error - {result['error']}")
                        )
                        
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f"  {test_case['name']}: Exception - {str(e)}")
                    )
        
        # Test ensemble prediction
        self.stdout.write(f'\n--- Testing Ensemble Prediction ---')
        try:
            result = make_prediction(
                test_cases[0]['features'], 
                use_ensemble=True
            )
            
            if result['success']:
                prediction_text = 'Heart Disease' if result['prediction'] == 1 else 'No Heart Disease'
                confidence = result['confidence'] * 100
                
                self.stdout.write(
                    f"Ensemble Result: {prediction_text} (Confidence: {confidence:.1f}%)"
                )
                
                if result['individual_predictions']:
                    self.stdout.write("Individual model predictions:")
                    for model, (pred, conf) in result['individual_predictions'].items():
                        pred_text = 'Heart Disease' if pred == 1 else 'No Heart Disease'
                        self.stdout.write(f"  {model}: {pred_text} ({conf*100:.1f}%)")
            else:
                self.stdout.write(
                    self.style.ERROR(f"Ensemble Error: {result['error']}")
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Ensemble Exception: {str(e)}")
            )
        
        # Test feature importance
        self.stdout.write(f'\n--- Testing Feature Importance ---')
        for model_name in available_models:
            importance = predictor.get_feature_importance(model_name)
            if importance:
                self.stdout.write(f"{model_name} - Top 3 important features:")
                sorted_features = sorted(importance.items(), key=lambda x: x[1], reverse=True)[:3]
                for feature, score in sorted_features:
                    self.stdout.write(f"  {feature}: {score:.4f}")
            else:
                self.stdout.write(f"{model_name}: No feature importance available")
        
        self.stdout.write(self.style.SUCCESS('\nModel testing completed!'))
