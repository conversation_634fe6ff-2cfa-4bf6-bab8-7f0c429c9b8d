from django.core.management.base import BaseCommand
from django.db import connection
from django.urls import reverse
from prediction.models import PatientData
from prediction.ml_utils import predictor, make_prediction
import os

class Command(BaseCommand):
    help = 'Comprehensive system check for Heart Disease Prediction System'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('🎓 Heart Disease Prediction System - System Check'))
        self.stdout.write('=' * 60)
        
        checks_passed = 0
        total_checks = 0
        
        # Check 1: Database connection
        total_checks += 1
        self.stdout.write('\n1. Testing database connection...')
        try:
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            self.stdout.write(self.style.SUCCESS(f'   ✅ Database connection successful: {result}'))
            checks_passed += 1
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Database connection failed: {e}'))
        
        # Check 2: Models
        total_checks += 1
        self.stdout.write('\n2. Testing Django models...')
        try:
            count = PatientData.objects.count()
            self.stdout.write(self.style.SUCCESS(f'   ✅ PatientData model working - Records: {count}'))
            checks_passed += 1
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ Model test failed: {e}'))
        
        # Check 3: ML Models
        total_checks += 1
        self.stdout.write('\n3. Testing ML models...')
        try:
            available_models = predictor.get_available_models()
            self.stdout.write(self.style.SUCCESS(f'   ✅ Available ML models: {available_models}'))
            
            if available_models:
                # Test prediction
                sample_features = [63, 1, 3, 145, 233, 1, 0, 150, 0, 2.3, 0, 0, 1]
                result = make_prediction(sample_features, model_name=available_models[0])
                if result['success']:
                    self.stdout.write(self.style.SUCCESS(f'   ✅ Sample prediction: {result["prediction"]} (confidence: {result["confidence"]:.2f})'))
                else:
                    self.stdout.write(self.style.WARNING(f'   ⚠️  Prediction failed: {result["error"]}'))
            checks_passed += 1
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ ML model test failed: {e}'))
        
        # Check 4: URL routing
        total_checks += 1
        self.stdout.write('\n4. Testing URL routing...')
        try:
            urls_to_test = ['prediction:home', 'prediction:form', 'prediction:history', 'prediction:about']
            for url_name in urls_to_test:
                url = reverse(url_name)
                self.stdout.write(f'   ✅ {url_name}: {url}')
            checks_passed += 1
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ URL routing test failed: {e}'))
        
        # Check 5: File structure
        total_checks += 1
        self.stdout.write('\n5. Checking file structure...')
        try:
            required_files = [
                'manage.py',
                'src/settings.py',
                'src/urls.py',
                'prediction/models.py',
                'prediction/views.py',
                'prediction/urls.py',
                'templates/base.html',
                'templates/prediction/home.html',
                'templates/prediction/form.html',
                'models/RandomForest_model.joblib'
            ]
            
            missing_files = []
            for file_path in required_files:
                if not os.path.exists(file_path):
                    missing_files.append(file_path)
            
            if not missing_files:
                self.stdout.write(self.style.SUCCESS('   ✅ All required files present'))
                checks_passed += 1
            else:
                self.stdout.write(self.style.ERROR(f'   ❌ Missing files: {missing_files}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'   ❌ File structure check failed: {e}'))
        
        # Summary
        self.stdout.write('\n' + '=' * 60)
        self.stdout.write(f'📊 System Check Results: {checks_passed}/{total_checks} checks passed')
        
        if checks_passed == total_checks:
            self.stdout.write(self.style.SUCCESS('🎉 All system checks passed!'))
            self.stdout.write(self.style.SUCCESS('🚀 System is ready for use!'))
            self.stdout.write('\n📋 To start the server:')
            self.stdout.write('   python manage.py runserver')
            self.stdout.write('   Then open: http://127.0.0.1:8000')
        else:
            self.stdout.write(self.style.WARNING(f'⚠️  {total_checks - checks_passed} checks failed'))
            self.stdout.write('Please review the errors above and fix them before running the server.')
        
        return checks_passed == total_checks
