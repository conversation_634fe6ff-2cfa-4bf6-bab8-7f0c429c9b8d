{% extends 'base.html' %}

{% block title %}Prediction History - Heart Disease Prediction System{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-lg-12">
            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-graph-up text-primary" style="font-size: 2rem;"></i>
                            <h3 class="mt-2">{{ total_predictions }}</h3>
                            <p class="text-muted">Total Predictions</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                            <h3 class="mt-2">{{ positive_predictions }}</h3>
                            <p class="text-muted">Positive Cases</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                            <h3 class="mt-2">{{ negative_predictions }}</h3>
                            <p class="text-muted">Negative Cases</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History Table -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="bi bi-clock-history"></i> Prediction History
                    </h3>
                </div>
                <div class="card-body">
                    {% if patients %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Patient Info</th>
                                    <th>Prediction</th>
                                    <th>Confidence</th>
                                    <th>Model Used</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for patient in patients %}
                                <tr>
                                    <td>{{ patient.id }}</td>
                                    <td>
                                        <strong>Age:</strong> {{ patient.age }}<br>
                                        <strong>Sex:</strong> {% if patient.sex == 1 %}Male{% else %}Female{% endif %}
                                    </td>
                                    <td>
                                        {% if patient.prediction_result == 1 %}
                                            <span class="badge bg-danger">Heart Disease</span>
                                        {% else %}
                                            <span class="badge bg-success">No Disease</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if patient.prediction_confidence %}
                                            <div class="progress" style="width: 100px; height: 20px;">
                                                <div class="progress-bar {% if patient.prediction_result == 1 %}bg-danger{% else %}bg-success{% endif %}" 
                                                     style="width: {{ patient.prediction_confidence|floatformat:1|mul:100 }}%">
                                                    {{ patient.prediction_confidence|floatformat:1|mul:100 }}%
                                                </div>
                                            </div>
                                        {% else %}
                                            N/A
                                        {% endif %}
                                    </td>
                                    <td>{{ patient.model_used|default:"N/A" }}</td>
                                    <td>{{ patient.created_at|date:"M d, Y H:i" }}</td>
                                    <td>
                                        <a href="{% url 'prediction:result' patient.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox" style="font-size: 4rem; color: #ccc;"></i>
                        <h4 class="mt-3 text-muted">No predictions yet</h4>
                        <p class="text-muted">Start by making your first prediction</p>
                        <a href="{% url 'prediction:form' %}" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> New Prediction
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Add filter functionality if needed
document.addEventListener('DOMContentLoaded', function() {
    // You can add filtering/sorting functionality here
});
</script>
{% endblock %}
