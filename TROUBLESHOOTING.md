# Heart Disease Prediction System - Troubleshooting Guide

## Issues Found and Fixed

### 1. Django Configuration Issues

**Problem**: ALLOWED_HOSTS was empty, which could prevent the server from accepting connections.

**Fix Applied**: Updated `src/settings.py`:
```python
ALLOWED_HOSTS = ['127.0.0.1', 'localhost', '0.0.0.0']
```

### 2. Template Syntax Error

**Problem**: Template syntax error in `templates/prediction/form.html` with malformed block tags.

**Fix Applied**: Corrected the template block structure:
```html
{% endblock %}

{% block extra_js %}
```

### 3. ML Model Loading Error Handling

**Problem**: ML model loading could fail silently or cause server startup issues.

**Fix Applied**: Enhanced error handling in `prediction/ml_utils.py`:
- Added directory existence check
- Improved error logging
- Graceful handling of missing model files

### 4. Database Configuration

**Problem**: Database might not be properly initialized.

**Fix Applied**: Ensured SQLite database configuration in `src/settings.py`:
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}
```

## Diagnostic Tools Created

### 1. System Check Command
```bash
python manage.py system_check
```
Comprehensive check of all system components.

### 2. Model Test Command
```bash
python manage.py test_models
```
Tests all ML models and prediction functionality.

### 3. Sample Data Creation
```bash
python manage.py create_sample_data --count 10
```
Creates sample patient data for testing.

### 4. Debug Scripts
- `debug_django.py` - Detailed Django component testing
- `fix_django.py` - Automated setup and fix script
- `start_server.py` - Enhanced server startup with error handling

## Common Issues and Solutions

### Issue: Server Won't Start

**Symptoms**: 
- `python manage.py runserver` exits immediately
- Connection refused errors
- No output from server command

**Solutions**:
1. Check for syntax errors in Python files:
   ```bash
   python -m py_compile src/settings.py
   python -m py_compile prediction/models.py
   python -m py_compile prediction/views.py
   ```

2. Run system check:
   ```bash
   python manage.py system_check
   ```

3. Check database migrations:
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

4. Use the enhanced startup script:
   ```bash
   python start_server.py
   ```

### Issue: ML Models Not Loading

**Symptoms**:
- Prediction errors
- "No models available" messages
- Model loading exceptions

**Solutions**:
1. Verify model files exist:
   ```bash
   ls models/
   ```
   Should show: `*.joblib` files

2. Test model loading:
   ```bash
   python manage.py test_models
   ```

3. Check model file permissions and format

### Issue: Template Errors

**Symptoms**:
- Template syntax errors
- Missing template errors
- CSS/JS not loading

**Solutions**:
1. Check template syntax in all `.html` files
2. Verify static files configuration
3. Run Django's built-in checks:
   ```bash
   python manage.py check
   ```

### Issue: Database Errors

**Symptoms**:
- Migration errors
- Database connection errors
- Model operation failures

**Solutions**:
1. Reset migrations (if needed):
   ```bash
   rm prediction/migrations/0*.py
   python manage.py makemigrations prediction
   python manage.py migrate
   ```

2. Check database file permissions
3. Verify SQLite installation

## Testing the Application

### 1. Start the Server
```bash
python manage.py runserver
# or
python start_server.py
```

### 2. Test URLs
- Home: http://127.0.0.1:8000/
- New Prediction: http://127.0.0.1:8000/predict/
- History: http://127.0.0.1:8000/history/
- About: http://127.0.0.1:8000/about/

### 3. Test Functionality
1. **Home Page**: Should show statistics and navigation
2. **Prediction Form**: Fill out patient data and submit
3. **Results Page**: Should show prediction with confidence
4. **History Page**: Should list previous predictions

### 4. Test Sample Data
```bash
python manage.py create_sample_data --count 5
```
Then check the history page for sample predictions.

## Performance Optimization

### 1. Static Files
Ensure static files are properly configured:
```bash
python manage.py collectstatic
```

### 2. Debug Mode
For production, set `DEBUG = False` in settings.py

### 3. Database Optimization
Consider using PostgreSQL for production instead of SQLite.

## Security Considerations

### 1. Secret Key
Change the Django secret key for production use.

### 2. Allowed Hosts
Update ALLOWED_HOSTS for your production domain.

### 3. HTTPS
Enable HTTPS in production environment.

## Support

If issues persist:
1. Check Django logs for detailed error messages
2. Verify all dependencies are installed correctly
3. Ensure Python virtual environment is activated
4. Check file permissions and paths
5. Review Django documentation for version-specific issues

## Files Modified/Created

### Modified Files:
- `src/settings.py` - Added ALLOWED_HOSTS
- `templates/prediction/form.html` - Fixed template syntax
- `prediction/ml_utils.py` - Enhanced error handling

### Created Files:
- `prediction/management/commands/system_check.py`
- `prediction/management/commands/test_models.py`
- `prediction/management/commands/create_sample_data.py`
- `debug_django.py`
- `fix_django.py`
- `start_server.py`
- `TROUBLESHOOTING.md` (this file)

The system should now be fully functional and ready for thesis defense demonstration.
