{% extends 'base.html' %}

{% block title %}About - Heart Disease Prediction System{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-4">About This System</h1>
                <p class="lead">
                    Advanced machine learning system for heart disease prediction developed as part of 
                    MSc Computer Science thesis at University of Ghana.
                </p>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- Project Overview -->
    <div class="row mb-5">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">
                        <i class="bi bi-info-circle"></i> Project Overview
                    </h3>
                </div>
                <div class="card-body">
                    <h4>Heart Disease Prediction Using Hybrid Machine Learning Models</h4>
                    <p class="lead">Early Detection Through Advanced Feature Selection and Ensemble Methods</p>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <h5>Research Objectives:</h5>
                            <ul>
                                <li>Develop accurate ML models for heart disease prediction</li>
                                <li>Compare different feature selection techniques</li>
                                <li>Implement ensemble methods for improved accuracy</li>
                                <li>Create a practical clinical decision support system</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Key Features:</h5>
                            <ul>
                                <li>Multiple ML algorithms (RF, SVM, LR, GB)</li>
                                <li>Real-time prediction capabilities</li>
                                <li>Confidence scoring for predictions</li>
                                <li>Feature importance analysis</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Models -->
    <div class="row mb-5">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h3 class="mb-0">
                        <i class="bi bi-cpu"></i> Available Machine Learning Models
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for model, description in model_descriptions.items %}
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-gear"></i> {{ model }}
                                        {% if model in available_models %}
                                            <span class="badge bg-success ms-2">Available</span>
                                        {% else %}
                                            <span class="badge bg-secondary ms-2">Unavailable</span>
                                        {% endif %}
                                    </h5>
                                    <p class="card-text">{{ description }}</p>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dataset Information -->
    <div class="row mb-5">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h3 class="mb-0">
                        <i class="bi bi-database"></i> Dataset Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Data Sources:</h5>
                            <ul>
                                <li><strong>Cleveland Heart Disease Dataset</strong> - UCI ML Repository</li>
                                <li><strong>Statlog Heart Disease Dataset</strong> - UCI ML Repository</li>
                            </ul>
                            
                            <h5 class="mt-4">Features Used (13 total):</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> Age</li>
                                <li><i class="bi bi-check-circle text-success"></i> Sex</li>
                                <li><i class="bi bi-check-circle text-success"></i> Chest Pain Type</li>
                                <li><i class="bi bi-check-circle text-success"></i> Resting Blood Pressure</li>
                                <li><i class="bi bi-check-circle text-success"></i> Serum Cholesterol</li>
                                <li><i class="bi bi-check-circle text-success"></i> Fasting Blood Sugar</li>
                                <li><i class="bi bi-check-circle text-success"></i> Resting ECG Results</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Additional Features:</h5>
                            <ul class="list-unstyled">
                                <li><i class="bi bi-check-circle text-success"></i> Maximum Heart Rate</li>
                                <li><i class="bi bi-check-circle text-success"></i> Exercise Induced Angina</li>
                                <li><i class="bi bi-check-circle text-success"></i> ST Depression</li>
                                <li><i class="bi bi-check-circle text-success"></i> ST Slope</li>
                                <li><i class="bi bi-check-circle text-success"></i> Major Vessels Count</li>
                                <li><i class="bi bi-check-circle text-success"></i> Thalassemia Test</li>
                            </ul>
                            
                            <h5 class="mt-4">Data Preprocessing:</h5>
                            <ul>
                                <li>Missing value imputation (MICE)</li>
                                <li>Data balancing (SMOTE-ENN)</li>
                                <li>Feature scaling and normalization</li>
                                <li>Outlier detection and removal</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Methodology -->
    <div class="row mb-5">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h3 class="mb-0">
                        <i class="bi bi-diagram-3"></i> Methodology
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Feature Selection Techniques:</h5>
                            <ul>
                                <li><strong>MRMR</strong> - Minimum Redundancy Maximum Relevance</li>
                                <li><strong>RFE</strong> - Recursive Feature Elimination</li>
                                <li><strong>GA</strong> - Genetic Algorithm</li>
                                <li><strong>Lasso</strong> - L1 Regularization</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>Performance Metrics:</h5>
                            <ul>
                                <li>Accuracy</li>
                                <li>F1-Score</li>
                                <li>Area Under ROC Curve (AUC)</li>
                                <li>Precision and Recall</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Author Information -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h3 class="mb-0">
                        <i class="bi bi-person-badge"></i> Author Information
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Student:</h5>
                            <p><strong>Gilles Ashley</strong><br>
                            Student ID: 22254414<br>
                            MSc Computer Science<br>
                            University of Ghana</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Supervisor:</h5>
                            <p><strong>Prof. Ebenezer Owusu</strong><br>
                            Department of Computer Science<br>
                            University of Ghana</p>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <h5>Thesis Title:</h5>
                            <p class="lead">"Heart Disease Prediction Using Hybrid Machine Learning Models: Early Detection Through Advanced Feature Selection and Ensemble Methods"</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
