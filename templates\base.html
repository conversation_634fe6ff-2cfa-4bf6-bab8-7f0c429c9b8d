<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      {% block title %}Heart Disease Prediction System{% endblock %}
    </title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css"
      rel="stylesheet"
    />
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Custom CSS -->
    {% load static %}
    <link href="{% static 'css/custom.css' %}" rel="stylesheet" />

    <style>
      .navbar-brand {
        font-weight: bold;
      }
      .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
      }
      .card {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
        transition: transform 0.2s;
      }
      .card:hover {
        transform: translateY(-2px);
      }
      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
      }
      .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
      .prediction-positive {
        color: #dc3545;
        font-weight: bold;
      }
      .prediction-negative {
        color: #28a745;
        font-weight: bold;
      }
      .confidence-bar {
        height: 20px;
        border-radius: 10px;
      }
      footer {
        background-color: #343a40;
        color: white;
        margin-top: 4rem;
      }
    </style>

    {% block extra_css %}{% endblock %}
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container">
        <a class="navbar-brand" href="{% url 'prediction:home' %}">
          <i class="bi bi-heart-pulse"></i> Heart Disease Prediction
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="{% url 'prediction:home' %}">
                <i class="bi bi-house"></i> Home
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{% url 'prediction:form' %}">
                <i class="bi bi-clipboard-data"></i> New Prediction
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{% url 'prediction:history' %}">
                <i class="bi bi-clock-history"></i> History
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{% url 'prediction:about' %}">
                <i class="bi bi-info-circle"></i> About
              </a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
      {% for message in messages %}
      <div
        class="alert alert-{{ message.tags }} alert-dismissible fade show"
        role="alert"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
        ></button>
      </div>
      {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main>{% block content %}{% endblock %}</main>

    <!-- Footer -->
    <footer class="py-4">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <h5>Heart Disease Prediction System</h5>
            <p class="mb-0">MSc Computer Science Thesis Project</p>
            <p class="mb-0">University of Ghana - Gilles Ashley</p>
          </div>
          <div class="col-md-6 text-md-end">
            <p class="mb-0">Built with Django & Machine Learning</p>
            <p class="mb-0">© 2025 All rights reserved</p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
