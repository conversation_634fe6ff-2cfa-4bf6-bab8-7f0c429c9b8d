#!/usr/bin/env python3
"""
Test script to validate the ML models and understand their structure
"""

import joblib
import numpy as np
import pandas as pd
import os

def test_model(model_path):
    """Test loading and basic functionality of a model"""
    print(f"\n=== Testing {model_path} ===")
    
    try:
        # Load the model
        model = joblib.load(model_path)
        print(f"✓ Model loaded successfully")
        print(f"Model type: {type(model)}")
        
        # Check model attributes
        if hasattr(model, 'n_features_in_'):
            print(f"Number of input features: {model.n_features_in_}")
        
        if hasattr(model, 'feature_names_in_'):
            print(f"Feature names: {model.feature_names_in_}")
        
        # Test prediction with sample data (13 features based on features.txt)
        # Sample patient data: age, sex, cp, trestbps, chol, fbs, restecg, thalach, exang, oldpeak, slope, ca, thal
        sample_data = np.array([[63, 1, 3, 145, 233, 1, 0, 150, 0, 2.3, 0, 0, 1]])
        
        try:
            prediction = model.predict(sample_data)
            print(f"✓ Sample prediction: {prediction}")
            
            # Try to get prediction probabilities if available
            if hasattr(model, 'predict_proba'):
                probabilities = model.predict_proba(sample_data)
                print(f"✓ Prediction probabilities: {probabilities}")
            
        except Exception as pred_error:
            print(f"✗ Prediction error: {pred_error}")
            
    except Exception as e:
        print(f"✗ Error loading model: {e}")

def main():
    """Test all models in the models directory"""
    models_dir = 'models'
    
    if not os.path.exists(models_dir):
        print(f"Models directory '{models_dir}' not found!")
        return
    
    # Get all .joblib files
    model_files = [f for f in os.listdir(models_dir) if f.endswith('.joblib')]
    
    if not model_files:
        print("No .joblib model files found!")
        return
    
    print(f"Found {len(model_files)} model files:")
    for model_file in model_files:
        print(f"  - {model_file}")
    
    # Test each model
    for model_file in model_files:
        model_path = os.path.join(models_dir, model_file)
        test_model(model_path)
    
    print("\n=== Model Testing Complete ===")

if __name__ == "__main__":
    main()
