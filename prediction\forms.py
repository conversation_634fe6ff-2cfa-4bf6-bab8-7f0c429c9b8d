from django import forms
from .models import PatientData

class PatientDataForm(forms.ModelForm):
    """Form for collecting patient data for heart disease prediction"""
    
    class Meta:
        model = PatientData
        fields = [
            'age', 'sex', 'cp', 'trestbps', 'chol', 'fbs', 
            'restecg', 'thalach', 'exang', 'oldpeak', 
            'slope', 'ca', 'thal'
        ]
        
        widgets = {
            'age': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter age (1-120)',
                'min': 1,
                'max': 120
            }),
            'sex': forms.Select(attrs={
                'class': 'form-select'
            }),
            'cp': forms.Select(attrs={
                'class': 'form-select'
            }),
            'trestbps': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter resting blood pressure (80-250 mm Hg)',
                'min': 80,
                'max': 250
            }),
            'chol': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter cholesterol level (100-600 mg/dl)',
                'min': 100,
                'max': 600
            }),
            'fbs': forms.Select(attrs={
                'class': 'form-select'
            }),
            'restecg': forms.Select(attrs={
                'class': 'form-select'
            }),
            'thalach': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter maximum heart rate (60-220)',
                'min': 60,
                'max': 220
            }),
            'exang': forms.Select(attrs={
                'class': 'form-select'
            }),
            'oldpeak': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter ST depression (0.0-10.0)',
                'min': 0.0,
                'max': 10.0,
                'step': 0.1
            }),
            'slope': forms.Select(attrs={
                'class': 'form-select'
            }),
            'ca': forms.Select(attrs={
                'class': 'form-select'
            }),
            'thal': forms.Select(attrs={
                'class': 'form-select'
            }),
        }
        
        labels = {
            'age': 'Age (years)',
            'sex': 'Sex',
            'cp': 'Chest Pain Type',
            'trestbps': 'Resting Blood Pressure (mm Hg)',
            'chol': 'Serum Cholesterol (mg/dl)',
            'fbs': 'Fasting Blood Sugar > 120 mg/dl',
            'restecg': 'Resting ECG Results',
            'thalach': 'Maximum Heart Rate Achieved',
            'exang': 'Exercise Induced Angina',
            'oldpeak': 'ST Depression Induced by Exercise',
            'slope': 'Slope of Peak Exercise ST Segment',
            'ca': 'Number of Major Vessels (0-3)',
            'thal': 'Thalassemia Test Result',
        }

class ModelSelectionForm(forms.Form):
    """Form for selecting which ML model to use for prediction"""
    
    MODEL_CHOICES = [
        ('RandomForest', 'Random Forest'),
        ('SVM', 'Support Vector Machine'),
        ('LogisticRegression', 'Logistic Regression'),
        ('GradientBoosting', 'Gradient Boosting'),
    ]
    
    selected_model = forms.ChoiceField(
        choices=MODEL_CHOICES,
        initial='RandomForest',
        widget=forms.Select(attrs={
            'class': 'form-select'
        }),
        label='Select ML Model'
    )
    
    use_ensemble = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input'
        }),
        label='Use Ensemble Prediction (Average of all models)'
    )
