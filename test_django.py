#!/usr/bin/env python3
"""
Test script to verify Django setup and ML models
"""

import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'src.settings')
django.setup()

def test_imports():
    """Test all required imports"""
    print("Testing imports...")
    try:
        import joblib
        import sklearn
        import pandas
        import numpy
        print("✅ All ML packages imported successfully")
        
        from django.conf import settings
        print(f"✅ Django settings loaded: {settings.DEBUG}")
        
        from prediction.models import PatientData
        from prediction.forms import PatientDataForm
        from prediction.ml_utils import predictor
        print("✅ Django app imports successful")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_database():
    """Test database connection"""
    print("\nTesting database...")
    try:
        from django.db import connection
        cursor = connection.cursor()
        cursor.execute("SELECT 1")
        print("✅ Database connection successful")
        
        from prediction.models import PatientData
        count = PatientData.objects.count()
        print(f"✅ Patient records in database: {count}")
        
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_models():
    """Test ML models"""
    print("\nTesting ML models...")
    try:
        from prediction.ml_utils import predictor, make_prediction
        
        available_models = predictor.get_available_models()
        print(f"✅ Available models: {available_models}")
        
        if available_models:
            # Test prediction with sample data
            sample_features = [63, 1, 3, 145, 233, 1, 0, 150, 0, 2.3, 0, 0, 1]
            result = make_prediction(sample_features, model_name=available_models[0])
            
            if result['success']:
                print(f"✅ Sample prediction successful: {result['prediction']} (confidence: {result['confidence']:.2f})")
            else:
                print(f"❌ Prediction failed: {result['error']}")
                return False
        else:
            print("⚠️  No models available")
            
        return True
    except Exception as e:
        print(f"❌ Model error: {e}")
        return False

def test_templates():
    """Test template rendering"""
    print("\nTesting templates...")
    try:
        from django.template.loader import get_template
        
        templates_to_test = [
            'base.html',
            'prediction/home.html',
            'prediction/form.html',
            'prediction/result.html',
            'prediction/history.html',
            'prediction/about.html'
        ]
        
        for template_name in templates_to_test:
            try:
                template = get_template(template_name)
                print(f"✅ Template found: {template_name}")
            except Exception as e:
                print(f"❌ Template error ({template_name}): {e}")
                return False
                
        return True
    except Exception as e:
        print(f"❌ Template system error: {e}")
        return False

def main():
    """Run all tests"""
    print("🎓 Heart Disease Prediction System - Django Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Database", test_database),
        ("ML Models", test_models),
        ("Templates", test_templates)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} test...")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} test failed")
    
    print(f"\n{'=' * 50}")
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Django setup is working correctly.")
        print("\n🚀 Ready to start the server with: python manage.py runserver")
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
